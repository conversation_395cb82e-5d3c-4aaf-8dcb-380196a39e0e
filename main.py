import os
import warnings
import logging

# Suppress TensorFlow warnings before importing any TensorFlow-dependent modules
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress TensorFlow INFO, WARNING, and ERROR messages
os.environ['TOKENIZERS_PARALLELISM'] = 'false'  # Suppress tokenizers warnings

# Suppress all warnings
warnings.filterwarnings('ignore')

# Configure logging to suppress verbose output from third-party libraries
logging.getLogger('tensorflow').setLevel(logging.ERROR)
logging.getLogger('transformers').setLevel(logging.ERROR)
logging.getLogger('sentence_transformers').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('urllib3.connectionpool').setLevel(logging.ERROR)
logging.getLogger('requests').setLevel(logging.ERROR)
logging.getLogger('httpx').setLevel(logging.ERROR)
logging.getLogger('httpcore').setLevel(logging.ERROR)
logging.getLogger('src.services.workload_analyzer').setLevel(logging.ERROR)

# Set root logger to ERROR to suppress all INFO, DEBUG, and WARNING messages
logging.getLogger().setLevel(logging.ERROR)

# Disable urllib3 debug output completely
import urllib3
urllib3.disable_warnings()

# Suppress HTTP connection debug output
import http.client
http.client.HTTPConnection.debuglevel = 0

import typer
from rich.prompt import IntPrompt
from src.config.config_manager import ConfigManager
from src.cli.cli import CLI
from src.services.analysis_service import AnalysisService
from src.services.api_service import APIService
from src.services.hardware_service import HardwareService
from src.models.analysis import AnalysisMethod # Kept as it might be used by CLI or services
from src.exceptions import ResearchToolError
from src.cli import input_handler

app = typer.Typer()

def _initialize_services():
    """Initializes and returns all necessary application services."""
    config = ConfigManager()
    cli = CLI()
    api_service = APIService(config) # APIService might use config
    analysis_service = AnalysisService(api_service) # AnalysisService might use APIService
    hardware_service = HardwareService()
    return config, cli, api_service, analysis_service, hardware_service

def _get_recursion_params(method: str):
    """Gets recursion parameters from the user if the method is recursive."""
    max_depth = 1
    max_breadth = 10
    if method == "recursive":
        max_depth = IntPrompt.ask(
            "Enter maximum recursion depth",
            default=3,
            show_default=True,
            choices=[str(i) for i in range(1, 11)]
        )
        max_breadth = IntPrompt.ask(
            "Enter maximum breadth (angles per topic/level)",
            default=3,
            show_default=True,
            choices=[str(i) for i in range(1, 11)]
        )
    return int(max_depth), int(max_breadth)

def _get_configuration_from_user(cli: CLI):
    """Gathers all necessary configuration inputs from the user via the CLI following Program_Plans.md flow."""
    # Show welcome screen
    cli.show_welcome()

    # Step 1: Output file specification
    output_file = cli.prompt_output_file()

    # Step 2: Hardware calibration choice
    use_hardware_calibration = cli.prompt_hardware_calibration()

    # Step 3: API selection
    api_choice = cli.prompt_api_choice()

    # Step 4: Analysis method selection
    method_choice_str = cli.prompt_method_choice()

    # Step 5: Input method and topics/angles
    input_method = cli.prompt_input_method()
    topics_and_angles = input_handler.get_topics_and_angles(input_method)
    cli.validator.validate_topics_and_angles(topics_and_angles)
    print(f"DEBUG: Topics and angles for analysis: {topics_and_angles}")

    max_depth, max_breadth = _get_recursion_params(method_choice_str)

    return output_file, api_choice, method_choice_str, topics_and_angles, max_depth, max_breadth, use_hardware_calibration

def _perform_hardware_calibration(cli: CLI, hardware_service: HardwareService, use_calibration: bool):
    """Performs hardware calibration based on user input."""
    calibration_result = None
    if use_calibration:
        cli.console.print()
        cli.console.print("[bold cyan]⚡ Running Hardware Calibration...[/bold cyan]")
        calibration_result = hardware_service.run_calibration()
        cli.console.print("[green]✓[/green] Hardware calibration completed")
    else:
        calibration_result = hardware_service.load_calibration()
        cli.console.print("[yellow]⚠[/yellow] Using existing calibration settings")
    # Note: calibration_result is not directly used further in the main flow in this version.
    # If analysis_service needs it, it should be passed along.
    return calibration_result

def _run_analysis_with_progress(
    cli: CLI,
    analysis_service: AnalysisService,
    topics_and_angles,
    method: str,
    api_choice,
    max_depth: int,
    max_breadth: int
):
    """Runs the analysis process with a progress bar display."""
    with cli.display_progress() as progress:
        total_tasks = sum(len(angles or ["General"]) for _, angles in topics_and_angles)
        print(f"DEBUG: Total analysis tasks: {total_tasks}")
        task_id = progress.add_task("Analyzing topics...", total=total_tasks)

        # Track current task for better progress updates
        current_task = {"count": 0}

        def update_progress_callback(topic: str, angle: str, is_starting: bool = True):
            """Enhanced progress callback with start/end tracking."""
            if is_starting:
                current_task["count"] += 1
                print(f"DEBUG: Starting task {current_task['count']}/{total_tasks}: {topic}/{angle}")
                progress.update(
                    task_id,
                    completed=current_task["count"] - 1,
                    description=f"Processing {topic}/{angle} ({current_task['count']}/{total_tasks})"
                )
            else:
                print(f"DEBUG: Completed task {current_task['count']}/{total_tasks}: {topic}/{angle}")
                progress.update(
                    task_id,
                    completed=current_task["count"],
                    description=f"Completed {topic}/{angle} ({current_task['count']}/{total_tasks})"
                )

        # Use the enhanced progress tracking with live updates
        results = cli.run_with_live_countdown(
            progress,
            task_id,
            analysis_service.analyze_topics,
            topics_and_angles,
            method,
            api_choice,
            progress_callback=update_progress_callback,
            max_depth=max_depth,
            max_breadth=max_breadth
        )
        print(f"DEBUG: Analysis results from service: {results}")
    return results

def _handle_results(cli: CLI, results, output_file: str):
    """Handles the display and saving of analysis results."""
    if not results:
        cli.console.print("[yellow]No analysis results to display or save.[/yellow]")
        return

    # Handle both dict and object formats for results
    result_text = "\n\n".join(
        f"## Topic: {r['topic'] if isinstance(r, dict) else r.topic}\n### Angle: {r['angle'] if isinstance(r, dict) else r.angle}\n{r['content'] if isinstance(r, dict) else r.content}"
        for r in results
    )
    print(f"DEBUG: Final result_text to be printed and saved:\n{result_text}")

    cli.console.print(f"Results saved to [bold]{output_file}[/bold] and displayed below:")
    cli.console.print(result_text, style="yellow")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(result_text)

@app.command()
def main():
    """
    Main entry point for the Deep Research Tool.
    Orchestrates the workflow: initialization, user input, calibration, analysis, and results handling.
    """
    cli = None # Initialize cli to None for broader scope in except block
    try:
        # 1. Initialize components
        _config, cli, _api_service, analysis_service, hardware_service = _initialize_services()

        # 2. Gather user inputs for configuration
        output_file, api_choice, method, topics_and_angles, max_depth, max_breadth, use_hardware_calibration = \
            _get_configuration_from_user(cli)

        # 3. Perform hardware calibration
        _calibration_result = _perform_hardware_calibration(cli, hardware_service, use_hardware_calibration)

        # 4. Run analysis
        results = _run_analysis_with_progress(
            cli, analysis_service, topics_and_angles, method, api_choice, max_depth, max_breadth
        )

        # 5. Output results
        _handle_results(cli, results, output_file)

    except ResearchToolError as e:
        if cli: # Check if cli was initialized
            cli.display_error(e)
        else:
            print(f"ResearchToolError: {e}") # Fallback if CLI is not available
        raise typer.Exit(code=1)
    except Exception as e:
        import traceback # Import traceback here for debugging
        error_message = f"An unexpected error occurred: {e}"
        if cli: # Check if cli was initialized
            cli.console.print(f"[bold red]{error_message}[/bold red]")
        else:
            print(error_message) # Fallback if CLI is not available
        print("\n--- Traceback of the original error that was caught by main.py: ---")
        traceback.print_exc() # Print the traceback of the current exception e
        print("--- End of traceback ---\n")
        raise typer.Exit(code=2)

if __name__ == "__main__":
    app()
