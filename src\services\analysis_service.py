import logging # Added to resolve NameError
from typing import List, Tuple, Dict, Any, Optional, Callable, Iterator, TypeVar, Iterable
from ..models.analysis import AnalysisMethod, AnalysisResult
from ..exceptions import APIError
from ..utils.retry import retry_with_backoff
from .workload_analyzer import WorkloadA<PERSON>yzer # Added to resolve NameError
from .api_service import APIService # Added to resolve APIService type annotation
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv
import os
import psutil
try:
    import GPUtil
except ImportError:
    GPUtil = None
from ..utils.performance import LRUCache, BatchPerformanceMetrics, StrategySelector, CircuitBreaker, ResourcePool
try:
    import spacy
    import nltk
except ImportError:
    spacy = None
    nltk = None

# Define a generic type for the chunks function
T = TypeVar('T')

def chunks(iterable: Iterable[T], size: int) -> List[List[T]]:
    """Split an iterable into chunks of specified size."""
    result = []
    chunk = []
    for item in iterable:
        chunk.append(item)
        if len(chunk) >= size:
            result.append(chunk)
            chunk = []
    if chunk:  # Don't forget the last chunk if it's not full
        result.append(chunk)
    return result

class AnalysisService:
    def __init__(self, api_service: 'APIService'):
        self.api_service = api_service
        self.logger = logging.getLogger(__name__)
        self.workload_analyzer = WorkloadAnalyzer(
            learning_enabled=True,
            learning_path='workload_learning.json'
        )
        self.resource_pool = ResourcePool()
        self.max_workers = self._calculate_optimal_workers()
        # Use a simple dictionary instead of LRUCache
        self.batch_size_cache = {}

        # Initialize NLP resources with lazy loading
        self._nlp = None
        self._wordnet = None
        # Use a simple dictionary instead of LRUCache
        self.nlp_cache = {}

        # Initialize batch metrics
        self.batch_metrics = BatchPerformanceMetrics()
        self.strategy_selector = StrategySelector()

        # Initialize error handling
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=30
        )

    def _calculate_optimal_workers(self) -> int:
        """Calculate optimal number of workers based on system resources."""
        cpu_count = psutil.cpu_count(logical=False)
        # Ensure cpu_count is not None, default to 1 if it is
        cpu_count = 1 if cpu_count is None else cpu_count
        ram_gb = psutil.virtual_memory().total / (1024 ** 3)

        # Start with CPU count
        optimal = cpu_count

        # Adjust based on RAM
        if ram_gb < 8:
            optimal = min(optimal, 2)
        elif ram_gb < 16:
            optimal = min(optimal, 4)

        # Consider GPU availability
        if GPUtil is not None:
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    optimal = min(optimal, len(gpus) * 2)
            except:
                pass

        return min(optimal, int(os.getenv('MAX_WORKERS', '10')))

    def _lazy_load_nlp(self):
        """Lazy load NLP resources."""
        try:
            if self._nlp is None and spacy is not None:
                self._nlp = spacy.load('en_core_web_sm')

            if self._wordnet is None and nltk is not None:
                try:
                    nltk.data.find('corpora/wordnet')
                except LookupError:
                    nltk.download('wordnet')
                self._wordnet = nltk.corpus.wordnet
        except Exception as e:
            self.logger.error(f"Error loading NLP resources: {e}")
            self._nlp = None
            self._wordnet = None

    def _get_nlp(self):
        """Get NLP processor with caching."""
        self._lazy_load_nlp()
        return self._nlp

    def _get_wordnet(self):
        """Get WordNet with caching."""
        self._lazy_load_nlp()
        return self._wordnet

    def _calculate_batch_size(self, tasks: List) -> int:
        """Calculate optimal batch size based on multiple factors."""
        # Check cache first
        tasks_hash = hash(str(tasks))  # Convert to string first for safer hashing
        if tasks_hash in self.batch_size_cache:
            return self.batch_size_cache[tasks_hash]

        # Calculate based on system resources
        ram_gb = psutil.virtual_memory().available / (1024 ** 3)
        cpu_load = psutil.cpu_percent(interval=0.1)

        # Base size
        base_size = int(os.getenv('BATCH_SIZE_BASE', '10'))

        # Adjust based on resources
        size = base_size
        if ram_gb < 4:
            size = max(1, size // 2)
        elif ram_gb > 8:
            size *= 2

        if cpu_load > 70:
            size = max(1, size // 2)

        # Store in cache
        self.batch_size_cache[tasks_hash] = size
        return size

    def _choose_split_strategy(self, tasks: List, failed_index: int) -> str:
        """Choose the best split strategy based on metrics."""
        # Get performance metrics
        metrics = self.batch_metrics.get_latest()

        # Select strategy based on current conditions
        if metrics:
            if metrics['error_rate'] > 0.2:
                return 'error_based'
            elif metrics['resource_utilization'] > 0.8:
                return 'resource_based'
            elif metrics['content_complexity'] > 0.7:
                return 'content_based'
            elif metrics['priority_variance'] > 0.5:
                return 'priority_based'

        # Use default strategy
        return 'hybrid'

    def _split_by_size(self, tasks: List, failed_index: int) -> List[List]:
        """Split tasks into chunks based on size."""
        if not tasks:
            return []

        # Default to splitting into chunks of 5
        chunk_size = max(1, len(tasks) // 5)
        return [tasks[i:i + chunk_size] for i in range(0, len(tasks), chunk_size)]

    def _split_by_resources(self, tasks: List, failed_index: int) -> List[List]:
        """Split tasks based on available system resources."""
        if not tasks:
            return []

        # Get available CPU and memory
        cpu_count = psutil.cpu_count(logical=False) or 1
        available_mem = psutil.virtual_memory().available / (1024 ** 3)  # in GB

        # Calculate optimal chunk size based on resources
        chunk_size = max(1, min(len(tasks), cpu_count * 2, int(available_mem * 2)))
        return [tasks[i:i + chunk_size] for i in range(0, len(tasks), chunk_size)]

    def _split_by_hybrid(self, tasks: List, failed_index: int) -> List[List]:
        """Hybrid strategy combining multiple approaches."""
        if not tasks:
            return []

        # Split by size first
        size_splits = self._split_by_size(tasks, failed_index)

        # Then split by resources within each size split
        result = []
        for split in size_splits:
            resource_splits = self._split_by_resources(split, failed_index)
            result.extend(resource_splits)

        return result if result else [tasks]  # Ensure we always return at least one chunk

    def _extract_sub_angles(self, content: str, topic: str, angle: str, max_breadth: int = 5) -> List[Dict[str, Any]]:
        """Extract sub-angles with optimizations.

        Args:
            content: The content to analyze
            topic: The main topic
            angle: The angle to analyze
            max_breadth: Maximum number of sub-angles to return (default: 5)
        """
        if not content or not topic:
            return []

        # Use generator for memory efficiency
        def extract_concepts():
            nlp = self._get_nlp()
            wordnet = self._get_wordnet()

            # Check if NLP resources are available
            if nlp is None:
                self.logger.warning("NLP processor not available, skipping concept extraction")
                return

            # Process content in chunks
            chunk_size = 1000  # Process 1000 characters at a time
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i + chunk_size]
                doc = nlp(chunk)

                for chunk in doc.noun_chunks:
                    if len(chunk.text.split()) > 1:
                        yield chunk.text

                for ent in doc.ents:
                    if ent.label_ in ['ORG', 'PERSON', 'PRODUCT', 'WORK_OF_ART']:
                        yield ent.text

        # Process concepts with caching
        sub_angles = []
        for concept in extract_concepts():
            # Check cache first
            cache_key = f"{topic}:{angle}:{concept}"
            if cache_key in self.nlp_cache:
                sub_angle = self.nlp_cache[cache_key]
                sub_angles.append(sub_angle)
                continue

            # Calculate metrics
            similarity = self._calculate_semantic_similarity(topic, concept)
            relevance = self._check_angle_relevance(angle, concept)

            if similarity > 0.6 and relevance:
                sub_angle = {
                    'text': concept,
                    'similarity': similarity,
                    'relevance': relevance,
                    'confidence': similarity * relevance
                }
                # Store in cache
                self.nlp_cache[cache_key] = sub_angle
                sub_angles.append(sub_angle)

        # Use generator for clustering to save memory
        def cluster_generator():
            yield from self._perform_semantic_clustering(sub_angles)

        # Process in chunks
        chunk_size = 10
        clustered_angles = []
        for chunk in chunks(cluster_generator(), chunk_size):
            clustered_angles.extend(chunk)

        return clustered_angles[:max_breadth]

    def _initialize_nltk(self):
        """Initialize NLTK resources."""
        if nltk is None:
            self.logger.warning("NLTK not available, skipping NLTK initialization")
            return

        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        try:
            nltk.data.find('corpora/wordnet')
        except LookupError:
            nltk.download('wordnet')

    def _calculate_semantic_similarity(self, topic: str, concept: str) -> float:
        """Calculate semantic similarity between topic and concept."""
        try:
            wordnet = self._get_wordnet()
            if not wordnet:
                return 0.0

            topic_synsets = wordnet.synsets(topic)
            concept_synsets = wordnet.synsets(concept)

            if not topic_synsets or not concept_synsets:
                return 0.0

            topic_synset = topic_synsets[0]
            concept_synset = concept_synsets[0]
            similarity = topic_synset.path_similarity(concept_synset)
            return similarity if similarity is not None else 0.0
        except Exception as e:
            self.logger.warning(f"Error calculating semantic similarity: {e}")
            return 0.0

    def _check_angle_relevance(self, angle: str, concept: str) -> float:
        """Check if concept is relevant to the current angle."""
        if not angle or angle.lower() == 'general':
            return 1.0

        # Use simple keyword matching for relevance
        angle_keywords = angle.lower().split()
        concept_keywords = concept.lower().split()

        # Calculate Jaccard similarity
        intersection = len(set(angle_keywords) & set(concept_keywords))
        union = len(set(angle_keywords) | set(concept_keywords))
        return intersection / union if union > 0 else 0.0

    def _perform_semantic_clustering(self, sub_angles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Perform semantic clustering on sub-angles to reduce redundancy."""
        from sklearn.cluster import DBSCAN
        import numpy as np

        if not sub_angles:
            return []

        # Create feature vectors for clustering
        features = np.array([
            [angle['similarity'], angle['relevance'], angle['confidence']]
            for angle in sub_angles
        ])

        # Perform clustering
        clustering = DBSCAN(eps=0.3, min_samples=2).fit(features)

        # Group angles by cluster
        clusters = {}
        for i, label in enumerate(clustering.labels_):
            if label not in clusters:
                clusters[label] = []
            clusters[label].append(sub_angles[i])

        # Select best angle from each cluster
        clustered_angles = []
        for cluster in clusters.values():
            if cluster:  # Skip noise points (-1 label)
                best_angle = max(cluster, key=lambda x: x['confidence'])
                clustered_angles.append(best_angle)

        return clustered_angles

    def _analyze_relationships(self, angle: str, other_angles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze relationships between angles."""
        relationships = {
            'related': [],
            'contradictory': [],
            'complementary': []
        }

        for other in other_angles:
            if other['text'] != angle:
                # Calculate relationship score
                score = self._calculate_relationship_score(angle, other['text'])

                if score > 0.8:
                    relationships['related'].append(other['text'])
                elif score < 0.2:
                    relationships['contradictory'].append(other['text'])
                elif 0.2 < score < 0.8:
                    relationships['complementary'].append(other['text'])

        return relationships

    def _calculate_relationship_score(self, angle1: str, angle2: str) -> float:
        """Calculate relationship score between two angles."""
        try:
            wordnet = self._get_wordnet()
            if not wordnet:
                return 0.0

            synsets1 = wordnet.synsets(angle1)
            synsets2 = wordnet.synsets(angle2)

            if not synsets1 or not synsets2:
                return 0.0

            synset1 = synsets1[0]
            synset2 = synsets2[0]

            # Calculate path similarity
            path_sim = synset1.path_similarity(synset2) or 0.0

            # Calculate other similarities if available
            lch_sim = 0.0
            wup_sim = 0.0

            try:
                lch_sim = synset1.lch_similarity(synset2) or 0.0
            except:
                pass

            try:
                wup_sim = synset1.wup_similarity(synset2) or 0.0
            except:
                pass

            # Calculate average of available similarities
            similarities = [s for s in [path_sim, lch_sim, wup_sim] if s > 0]
            return sum(similarities) / len(similarities) if similarities else 0.0

        except Exception as e:
            self.logger.warning(f"Error calculating relationship score: {e}")
            return 0.0

    def _extract_context(self, content: str, angle: str) -> str:
        """Extract relevant context from the main analysis."""
        from nltk.tokenize import sent_tokenize

        sentences = sent_tokenize(content)
        context = []

        for sentence in sentences:
            if angle.lower() in sentence.lower():
                context.append(sentence)

        return ' '.join(context)

    def analyze_topics(self, topics_and_angles: List[Tuple[str, List[str]]], method: str, api_choice: str, progress_callback: Optional[Callable[[str, str, bool], None]] = None, max_depth: int = 3, max_breadth: int = 5) -> List[Dict]:
        """
        Analyze multiple topics and their angles using the specified method.
        Calls the LLM API for each topic/angle and returns results in a list of dicts.

        Args:
            topics_and_angles: List of (topic, angles) tuples
            method: Analysis method (recursive or iterative)
            api_choice: API to use (perplexity or ollama)
            progress_callback: Optional callback function to update progress (topic, angle, is_starting)
            max_depth: Maximum recursion depth for recursive analysis
            max_breadth: Maximum breadth for recursive analysis

        Returns:
            List of analysis results
        """
        # Note: max_depth and max_breadth are reserved for future recursive implementation
        # Currently, the method performs simple topic/angle analysis without deep recursion
        _ = max_depth, max_breadth  # Acknowledge parameters to avoid linting warnings

        results = []
        for topic, angles in topics_and_angles:
            for angle in (angles or ["General"]):
                # Call progress callback at start of task
                if progress_callback:
                    progress_callback(topic, angle, True)  # is_starting=True

                if method == "recursive":
                    prompt = self._generate_recursive_prompt(topic, angle)
                else:
                    prompt = self._generate_iterative_prompt(topic, angle)
                try:
                    content = self.api_service.call_api(prompt, api_choice)
                except Exception as e:
                    content = f"[ERROR] API call failed: {e}"

                # Call progress callback at end of task
                if progress_callback:
                    progress_callback(topic, angle, False)  # is_starting=False

                results.append({
                    "topic": topic,
                    "angle": angle,
                    "content": content
                })
        return results

    def _generate_recursive_prompt(self, topic: str, angle: str) -> str:
        return (
            f"Analyze the topic '{topic}' from the angle: '{angle}'. "
            "Provide a comprehensive analysis that explores various facets, key considerations, and potential implications. "
            "Structure your response in a clear, detailed markdown format."
        )

    def _generate_iterative_prompt(self, topic: str, angle: str) -> str:
        return (
            f"Analyze the topic '{topic}' from the angle: '{angle}'. "
            "Provide a comprehensive analysis that explores various facets, key considerations, and potential implications. "
            "Structure your response in a clear, detailed markdown format."
        )
