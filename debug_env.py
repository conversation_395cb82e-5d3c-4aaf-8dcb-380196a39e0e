#!/usr/bin/env python3
"""Debug script to check environment variable loading."""

import os
from dotenv import load_dotenv
from src.config.config_manager import ConfigManager

def main():
    print("=== Environment Variable Debug ===")
    
    # Check before loading .env
    print(f"OLLAMA_MODEL before load_dotenv: {repr(os.getenv('OLLAMA_MODEL'))}")
    
    # Load .env file
    load_dotenv()
    print(f"OLLAMA_MODEL after load_dotenv: {repr(os.getenv('OLLAMA_MODEL'))}")
    
    # Test ConfigManager
    try:
        config = ConfigManager()
        print(f"ConfigManager.ollama_model: {repr(config.ollama_model)}")
    except Exception as e:
        print(f"Error creating ConfigManager: {e}")
    
    # Check all environment variables containing 'OLLAMA'
    print("\n=== All OLLAMA-related environment variables ===")
    for key, value in os.environ.items():
        if 'OLLAMA' in key.upper():
            print(f"{key}: {repr(value)}")

if __name__ == "__main__":
    main()
